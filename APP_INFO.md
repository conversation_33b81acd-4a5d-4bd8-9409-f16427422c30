# Stock Prediction App - Technical Overview

## 📋 Application Summary

A modern full-stack stock prediction platform that combines Django REST API backend with React frontend, featuring AI-powered stock predictions, portfolio management, and real-time market data visualization.

## 🏗️ Architecture Overview

### Backend (Django 4.2)
- **Framework**: Django 4.2.10 with Django REST Framework
- **Authentication**: JWT-based authentication using SimpleJWT
- **Database**: SQLite (development) - easily configurable for PostgreSQL in production
- **API**: RESTful API with comprehensive endpoints for stocks, portfolio, and predictions
- **Admin Interface**: Full Django admin panel for data management

### Frontend (React 18)
- **Framework**: React 18 with modern hooks and functional components
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS for responsive, utility-first styling
- **Routing**: React Router DOM for client-side navigation
- **HTTP Client**: Axios for API communication
- **Charts**: Recharts for data visualization
- **Icons**: Heroicons for consistent iconography

## 🔧 Key Features

### Core Functionality
- **User Authentication**: Secure JWT-based login/registration system
- **Stock Management**: Track stocks with real-time price data
- **Portfolio Management**: Manage investment portfolios with profit/loss tracking
- **Watchlist**: Monitor favorite stocks with custom notes
- **AI Predictions**: Machine learning-powered stock price predictions
- **Responsive Design**: Mobile-first approach with Tailwind CSS

### Admin Features
- **Stock Administration**: Manage stock symbols, names, sectors, and industries
- **Price Data Management**: Handle historical and real-time stock prices
- **Prediction Management**: Oversee AI model predictions and confidence scores
- **User Portfolio Oversight**: Monitor user portfolios and transactions
- **Watchlist Management**: View and manage user watchlists

## 🚀 How to Run the Server

### Quick Setup (Automated)
```bash
# Make setup script executable and run
chmod +x setup.sh
./setup.sh
```

### Manual Setup

#### Backend Setup
1. **Create and activate virtual environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Database setup**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

4. **Create admin user**
   ```bash
   python manage.py createsuperuser
   # Or use default: admin/admin (created by setup.sh)
   ```

5. **Start Django server**
   ```bash
   python manage.py runserver
   ```
   Backend available at: `http://localhost:8000`

#### Frontend Setup
1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install Node.js dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```
   Frontend available at: `http://localhost:3000`

### Production Build
```bash
# Build frontend for production
cd frontend
npm run build

# Collect static files (Django)
cd ..
python manage.py collectstatic
```

## 🔗 Access Points

- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Django Admin Panel**: http://localhost:8000/admin
  - Default credentials: `admin` / `admin` (if using setup.sh)

## 📊 Database Models

### Core Models
- **Stock**: Company information (symbol, name, sector, industry)
- **StockPrice**: Historical price data (OHLC, volume)
- **StockPrediction**: AI predictions with confidence scores
- **UserPortfolio**: User investment tracking
- **Watchlist**: User stock monitoring
- **Profile**: Extended user information

### Admin Interface Features
- **Filtering**: Filter by date, stock, user, sector
- **Search**: Search across symbols, names, users
- **Sorting**: Customizable column sorting
- **Bulk Actions**: Mass operations on selected items

## 🛠️ Development Tools

### Backend
- **Django Admin**: Full CRUD operations for all models
- **DRF Browsable API**: Interactive API documentation
- **JWT Authentication**: Secure token-based auth
- **CORS Headers**: Cross-origin request handling

### Frontend
- **Vite Dev Server**: Hot module replacement
- **ESLint**: Code quality and consistency
- **Tailwind CSS**: Utility-first styling
- **Component Architecture**: Modular React components

## 📁 Project Structure

```
stock_predictor/
├── stock_predictor/          # Django project settings
├── stocks/                   # Main Django app
│   ├── models.py            # Database models
│   ├── views.py             # API views
│   ├── admin.py             # Admin configuration
│   └── serializers.py       # DRF serializers
├── frontend/                 # React application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── contexts/        # React contexts
│   │   └── services/        # API services
│   └── package.json         # Node dependencies
├── requirements.txt         # Python dependencies
├── setup.sh                # Automated setup script
└── README.md               # Detailed documentation
```

## 🔐 Admin Panel Features

### Stock Management
- Add/edit stock symbols and company information
- Manage sector and industry classifications
- Track creation and modification dates

### Price Data Administration
- Import and manage historical price data
- Monitor daily price updates
- View volume and OHLC data

### User Management
- View user portfolios and performance
- Monitor watchlist activities
- Manage user profiles and preferences

### Prediction Oversight
- Review AI model predictions
- Monitor confidence scores
- Track prediction accuracy

## 🚦 API Endpoints

### Authentication
- `POST /api/token/` - User login
- `POST /api/token/refresh/` - Token refresh
- `GET /api/users/me/` - Current user info

### Stock Data
- `GET /api/stocks/` - List all stocks
- `GET /api/stocks/{id}/` - Stock details
- `GET /api/stocks/{id}/prices/` - Price history
- `GET /api/stocks/{id}/predictions/` - AI predictions

### Portfolio & Watchlist
- `GET /api/portfolio/` - User portfolio
- `GET /api/watchlist/` - User watchlist
- `POST /api/portfolio/` - Add to portfolio
- `POST /api/watchlist/add_stock/` - Add to watchlist

## 💡 Development Notes

- **Environment**: Uses python-decouple for environment variable management
- **CORS**: Configured for local development (localhost:3000)
- **Database**: SQLite for development, easily configurable for production
- **Static Files**: Served by Django in development
- **Authentication**: JWT tokens with refresh capability
- **Error Handling**: Comprehensive error responses from API

## 🔧 Configuration

### Environment Variables
Create `.env` file in root directory:
```env
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
```

### Production Considerations
- Set `DEBUG=False`
- Configure production database (PostgreSQL recommended)
- Set up proper static file serving (nginx)
- Configure CORS for production domain
- Use environment variables for sensitive settings
