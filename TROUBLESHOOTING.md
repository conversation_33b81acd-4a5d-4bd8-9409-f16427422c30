# Troubleshooting Guide

## Common Issues and Solutions

### 1. Missing Dependencies Error
**Error**: `ModuleNotFoundError: No module named 'yfinance'` or similar

**Solution**:
```bash
# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install missing dependencies
pip install -r requirements.txt
```

### 2. Database Migration Issues
**Error**: `django.db.utils.OperationalError` or migration conflicts

**Solution**:
```bash
# Reset migrations (WARNING: This will delete all data)
rm stocks/migrations/0*.py
python manage.py makemigrations stocks
python manage.py migrate

# Or run the fix script
python fix_issues.py
```

### 3. Frontend Build Issues
**Error**: Frontend not loading or showing blank page

**Solution**:
```bash
cd frontend
npm install
npm run build
cd ..
python manage.py collectstatic --noinput
```

### 4. CORS Issues
**Error**: `Access to XMLHttpRequest blocked by CORS policy`

**Solution**: Check that `corsheaders` is properly configured in `settings.py`:
```python
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]
```

### 5. JWT Authentication Issues
**Error**: `Authentication credentials were not provided`

**Solution**:
1. Check that JWT tokens are being sent in requests
2. Verify token endpoints are working:
   ```bash
   curl -X POST http://localhost:8000/api/token/ \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin"}'
   ```

### 6. Static Files Not Loading
**Error**: CSS/JS files not loading in production

**Solution**:
```bash
python manage.py collectstatic --noinput
```

### 7. Port Already in Use
**Error**: `Error: That port is already in use`

**Solution**:
```bash
# Find and kill process using the port
lsof -ti:8000 | xargs kill -9  # For port 8000
lsof -ti:3000 | xargs kill -9  # For port 3000

# Or use different ports
python manage.py runserver 8001
npm run dev -- --port 3001
```

### 8. Permission Denied on setup.sh
**Error**: `Permission denied: ./setup.sh`

**Solution**:
```bash
chmod +x setup.sh
./setup.sh
```

### 9. Node.js/npm Issues
**Error**: `npm: command not found` or Node.js version issues

**Solution**:
1. Install Node.js 16+ from https://nodejs.org/
2. Or use nvm:
   ```bash
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   nvm install 18
   nvm use 18
   ```

### 10. Python Version Issues
**Error**: Python version compatibility issues

**Solution**:
- Ensure Python 3.8+ is installed
- Use pyenv for version management:
  ```bash
  pyenv install 3.9.16
  pyenv local 3.9.16
  ```

## Quick Fixes

### Reset Everything
```bash
# Stop all servers
# Delete virtual environment
rm -rf venv

# Delete node_modules
rm -rf frontend/node_modules

# Delete database
rm db.sqlite3

# Run setup again
./setup.sh
```

### Check System Status
```bash
# Check Python version
python3 --version

# Check Node.js version
node --version

# Check npm version
npm --version

# Check if ports are free
lsof -i :8000
lsof -i :3000
```

### Verify Installation
```bash
# Test Django
python manage.py check

# Test database connection
python manage.py dbshell

# Test frontend build
cd frontend && npm run build
```

## Getting Help

If you're still experiencing issues:

1. Check the console/terminal for error messages
2. Look at Django logs for backend issues
3. Check browser developer tools for frontend issues
4. Ensure all dependencies are installed correctly
5. Try running the fix script: `python fix_issues.py`

## Development Tips

- Always activate the virtual environment before running Python commands
- Use `python manage.py runserver` for backend development
- Use `npm run dev` for frontend development with hot reload
- Check `http://localhost:8000/admin` for database management
- Use browser developer tools to debug frontend issues
