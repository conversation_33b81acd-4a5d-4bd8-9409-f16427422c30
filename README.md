# Stock Predictor

A modern stock prediction platform built with Django 4.2 and React 18, featuring AI-powered stock predictions, portfolio management, and real-time market data.

## Features

- **Django 4.2 Backend**: RESTful API with Django REST Framework
- **React 18 Frontend**: Modern UI with Vite and Tailwind CSS
- **JWT Authentication**: Secure token-based authentication
- **Stock Management**: Track stocks, prices, and predictions
- **Portfolio Management**: Manage your investment portfolio
- **Watchlist**: Monitor favorite stocks
- **AI Predictions**: Machine learning-powered stock predictions
- **Responsive Design**: Mobile-first design with Tailwind CSS

## Tech Stack

### Backend
- Django 4.2.10
- Django REST Framework 3.14.0
- SimpleJWT 5.3.0 (JWT Authentication)
- Django CORS Headers 4.3.1
- SQLite (Development Database)

### Frontend
- React 18
- Vite (Build Tool)
- Tailwind CSS (Styling)
- React Router DOM (Routing)
- Axios (HTTP Client)
- Recharts (Charts)
- Heroicons (Icons)

## Project Structure

```
stock_predictor/
├── stock_predictor/          # Django project settings
├── stocks/                   # Django app
│   ├── models.py            # Database models
│   ├── views.py             # API views
│   ├── serializers.py       # DRF serializers
│   ├── urls.py              # URL routing
│   └── admin.py             # Admin interface
├── frontend/                 # React application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── contexts/        # React contexts
│   │   ├── services/        # API services
│   │   └── App.jsx          # Main app component
│   ├── public/              # Static assets
│   └── package.json         # Node dependencies
├── templates/               # Django templates
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## Setup Instructions

### Prerequisites

- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd stock_predictor
   ```

2. **Create and activate virtual environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run database migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

5. **Create superuser (optional)**
   ```bash
   python manage.py createsuperuser
   ```

6. **Run the Django development server**
   ```bash
   python manage.py runserver
   ```

The Django backend will be available at `http://localhost:8000`

### Frontend Setup

1. **Navigate to the frontend directory**
   ```bash
   cd frontend
   ```

2. **Install Node.js dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

The React frontend will be available at `http://localhost:3000`

### Production Build

1. **Build the React frontend**
   ```bash
   cd frontend
   npm run build
   ```

2. **Collect static files**
   ```bash
   cd ..
   python manage.py collectstatic
   ```

3. **Run Django with production settings**
   ```bash
   python manage.py runserver
   ```

## API Endpoints

### Authentication
- `POST /api/token/` - Login
- `POST /api/token/refresh/` - Refresh token
- `GET /api/users/me/` - Get current user

### Stocks
- `GET /api/stocks/` - List all stocks
- `GET /api/stocks/{id}/` - Get stock details
- `GET /api/stocks/search/?q={query}` - Search stocks
- `GET /api/stocks/{id}/prices/` - Get stock prices
- `GET /api/stocks/{id}/predictions/` - Get stock predictions

### Portfolio
- `GET /api/portfolio/` - Get user portfolio
- `POST /api/portfolio/` - Add to portfolio
- `PUT /api/portfolio/{id}/` - Update portfolio item
- `DELETE /api/portfolio/{id}/` - Remove from portfolio
- `GET /api/portfolio/summary/` - Get portfolio summary

### Watchlist
- `GET /api/watchlist/` - Get user watchlist
- `POST /api/watchlist/add_stock/` - Add stock to watchlist
- `DELETE /api/watchlist/remove_stock/` - Remove from watchlist

## Database Models

### Stock
- `symbol` - Stock symbol (e.g., AAPL)
- `name` - Company name
- `sector` - Industry sector
- `industry` - Industry classification

### StockPrice
- `stock` - Foreign key to Stock
- `date` - Price date
- `open_price`, `high_price`, `low_price`, `close_price` - OHLC data
- `volume` - Trading volume

### StockPrediction
- `stock` - Foreign key to Stock
- `prediction_date` - Date of prediction
- `predicted_price` - Predicted price
- `confidence_score` - Model confidence
- `model_used` - ML model name

### UserPortfolio
- `user` - Foreign key to User
- `stock` - Foreign key to Stock
- `shares` - Number of shares
- `average_price` - Average purchase price

### Watchlist
- `user` - Foreign key to User
- `stock` - Foreign key to Stock
- `notes` - User notes

## Development

### Adding New Features

1. **Backend**: Add models, views, and serializers in the `stocks` app
2. **Frontend**: Create components in `frontend/src/components/`
3. **API**: Update API services in `frontend/src/services/api.js`

### Code Style

- **Python**: Follow PEP 8 guidelines
- **JavaScript**: Use ESLint and Prettier
- **CSS**: Use Tailwind CSS utility classes

### Testing

```bash
# Backend tests
python manage.py test

# Frontend tests (when implemented)
cd frontend
npm test
```

## Deployment

### Environment Variables

Create a `.env` file in the root directory:

```env
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com
```

### Production Settings

1. Update `DEBUG = False` in settings.py
2. Configure production database (PostgreSQL recommended)
3. Set up static file serving (nginx recommended)
4. Configure CORS settings for production domain

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue on GitHub.

---

**Note**: This is a development setup. For production deployment, additional security measures and optimizations should be implemented. 