from rest_framework import serializers
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from .models import Stock, StockPrice, StockPrediction, UserPortfolio, Watchlist, Profile, StockData, Portfolio, PriceAlert
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer, TokenRefreshSerializer


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model"""
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']
        read_only_fields = ['id']


class StockSerializer(serializers.ModelSerializer):
    """Serializer for Stock model"""
    class Meta:
        model = Stock
        fields = '__all__'


class StockPriceSerializer(serializers.ModelSerializer):
    """Serializer for StockPrice model"""
    stock_symbol = serializers.CharField(source='stock.symbol', read_only=True)
    
    class Meta:
        model = StockPrice
        fields = '__all__'


class StockPredictionSerializer(serializers.ModelSerializer):
    """Serializer for StockPrediction model"""
    stock_symbol = serializers.CharField(source='stock.symbol', read_only=True)
    stock_name = serializers.CharField(source='stock.name', read_only=True)
    
    class Meta:
        model = StockPrediction
        fields = '__all__'


class UserPortfolioSerializer(serializers.ModelSerializer):
    """Serializer for UserPortfolio model"""
    stock_symbol = serializers.CharField(source='stock.symbol', read_only=True)
    stock_name = serializers.CharField(source='stock.name', read_only=True)
    total_value = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    profit_loss = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    current_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = UserPortfolio
        fields = '__all__'
        read_only_fields = ['user']


class WatchlistSerializer(serializers.ModelSerializer):
    """Serializer for Watchlist model"""
    stock_symbol = serializers.CharField(source='stock.symbol', read_only=True)
    stock_name = serializers.CharField(source='stock.name', read_only=True)
    current_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    price_change = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    price_change_percent = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = Watchlist
        fields = '__all__'
        read_only_fields = ['user']


class StockDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for Stock with related data"""
    latest_price = serializers.SerializerMethodField()
    latest_prediction = serializers.SerializerMethodField()
    price_history = serializers.SerializerMethodField()
    
    class Meta:
        model = Stock
        fields = '__all__'
    
    def get_latest_price(self, obj):
        """Get the latest price for the stock"""
        latest = obj.prices.first()
        if latest:
            return {
                'date': latest.date,
                'close_price': latest.close_price,
                'volume': latest.volume,
                'change': 0,  # Would need to calculate from previous day
                'change_percent': 0
            }
        return None
    
    def get_latest_prediction(self, obj):
        """Get the latest prediction for the stock"""
        latest = obj.predictions.filter(is_active=True).first()
        if latest:
            return {
                'prediction_date': latest.prediction_date,
                'predicted_price': latest.predicted_price,
                'confidence_score': latest.confidence_score,
                'model_used': latest.model_used
            }
        return None
    
    def get_price_history(self, obj):
        """Get recent price history for the stock"""
        prices = obj.prices.order_by('-date')[:30]  # Last 30 days
        return StockPriceSerializer(prices, many=True).data 

class ProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ['email_notifications']

class RegisterSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    password2 = serializers.CharField(write_only=True, required=True)
    email_notifications = serializers.BooleanField(default=True)

    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'password2', 'email_notifications')

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({'password': "Passwords do not match."})
        if User.objects.filter(username=attrs['username']).exists():
            raise serializers.ValidationError({'username': "Username already taken."})
        if User.objects.filter(email=attrs['email']).exists():
            raise serializers.ValidationError({'email': "Email already registered."})
        return attrs

    def create(self, validated_data):
        email_notifications = validated_data.pop('email_notifications', True)
        validated_data.pop('password2')
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=validated_data['password']
        )
        Profile.objects.create(user=user, email_notifications=email_notifications)
        return user

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        data['user'] = {
            'id': self.user.id,
            'username': self.user.username,
            'email': self.user.email,
        }
        return data

class CustomTokenRefreshSerializer(TokenRefreshSerializer):
    pass 

class StockDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = StockData
        fields = ['ticker', 'date', 'open_price', 'close_price', 'high', 'low', 'volume'] 

class PortfolioSerializer(serializers.ModelSerializer):
    class Meta:
        model = Portfolio
        fields = ['id', 'user', 'ticker', 'quantity', 'purchase_price', 'purchase_date']
        read_only_fields = ['user']

    def validate_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("Quantity must be positive.")
        return value 

class PriceAlertSerializer(serializers.ModelSerializer):
    class Meta:
        model = PriceAlert
        fields = ['id', 'user', 'ticker', 'threshold']
        read_only_fields = ['user'] 