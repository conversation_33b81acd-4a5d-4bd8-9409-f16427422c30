# Generated by Django 4.2.10 on 2025-07-20 17:09

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('stocks', '0004_pricealert_portfolio'),
    ]

    operations = [
        migrations.AddField(
            model_name='pricealert',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='pricealert',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
    ]
