from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth.models import User
from .models import Portfolio, StockData
from rest_framework_simplejwt.tokens import RefreshToken
import datetime

# Create your tests here.

class APITestBase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass123', email='<EMAIL>')
        self.client = APIClient()
        refresh = RefreshToken.for_user(self.user)
        self.token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')

class StockDataAPITests(APITestBase):
    def test_valid_ticker(self):
        response = self.client.get('/api/stocks/AAPL/')
        self.assertIn(response.status_code, [200, 400])  # 200 if data, 400 if yfinance fails

    def test_invalid_ticker(self):
        response = self.client.get('/api/stocks/INVALIDTICKER/')
        self.assertEqual(response.status_code, 400)
        self.assertIn('error', response.json())

class PredictAPITests(APITestBase):
    def test_predict_invalid_ticker(self):
        response = self.client.get('/api/predict/INVALIDTICKER/')
        self.assertIn(response.status_code, [400, 404])
        self.assertIn('error', response.json())

class PortfolioAPITests(APITestBase):
    def test_create_portfolio_valid(self):
        data = {
            'ticker': 'AAPL',
            'quantity': 10,
            'purchase_price': 100.0,
            'purchase_date': '2023-01-01',
        }
        response = self.client.post('/api/portfolios/', data)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(Portfolio.objects.count(), 1)

    def test_create_portfolio_invalid_quantity(self):
        data = {
            'ticker': 'AAPL',
            'quantity': -5,
            'purchase_price': 100.0,
            'purchase_date': '2023-01-01',
        }
        response = self.client.post('/api/portfolios/', data)
        self.assertEqual(response.status_code, 400)
        self.assertIn('quantity', response.json())

    def test_list_portfolios(self):
        Portfolio.objects.create(user=self.user, ticker='AAPL', quantity=5, purchase_price=120.0, purchase_date='2023-01-01')
        response = self.client.get('/api/portfolios/')
        self.assertEqual(response.status_code, 200)
        self.assertTrue(isinstance(response.json(), list))
        self.assertEqual(response.json()[0]['ticker'], 'AAPL')
