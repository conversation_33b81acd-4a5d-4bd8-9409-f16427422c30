from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    email_notifications = models.BooleanField(default=True)

    def __str__(self):
        return f"Profile for {self.user.username}"


class Stock(models.Model):
    """Model for storing stock information"""
    symbol = models.CharField(max_length=10, unique=True)
    name = models.CharField(max_length=200)
    sector = models.CharField(max_length=100, blank=True, null=True)
    industry = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.symbol} - {self.name}"

    class Meta:
        ordering = ['symbol']


class StockPrice(models.Model):
    """Model for storing historical stock prices"""
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE, related_name='prices')
    date = models.DateField()
    open_price = models.DecimalField(max_digits=10, decimal_places=2)
    high_price = models.DecimalField(max_digits=10, decimal_places=2)
    low_price = models.DecimalField(max_digits=10, decimal_places=2)
    close_price = models.DecimalField(max_digits=10, decimal_places=2)
    volume = models.BigIntegerField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['stock', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.stock.symbol} - {self.date} - ${self.close_price}"


class StockPrediction(models.Model):
    """Model for storing stock price predictions"""
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE, related_name='predictions')
    prediction_date = models.DateField()
    predicted_price = models.DecimalField(max_digits=10, decimal_places=2)
    confidence_score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    model_used = models.CharField(max_length=100, default='LSTM')
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['stock', 'prediction_date']
        ordering = ['-prediction_date']

    def __str__(self):
        return f"{self.stock.symbol} - {self.prediction_date} - ${self.predicted_price}"


class UserPortfolio(models.Model):
    """Model for storing user's stock portfolio"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='portfolios')
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE)
    shares = models.DecimalField(max_digits=10, decimal_places=2)
    average_price = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'stock']
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.user.username} - {self.stock.symbol} - {self.shares} shares"

    @property
    def total_value(self):
        """Calculate total value of this portfolio entry"""
        latest_price = self.stock.prices.first()
        if latest_price:
            return self.shares * latest_price.close_price
        return 0

    @property
    def profit_loss(self):
        """Calculate profit/loss for this portfolio entry"""
        latest_price = self.stock.prices.first()
        if latest_price:
            current_value = self.shares * latest_price.close_price
            cost_basis = self.shares * self.average_price
            return current_value - cost_basis
        return 0


class Watchlist(models.Model):
    """Model for storing user's watchlist"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='watchlists')
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        unique_together = ['user', 'stock']
        ordering = ['-added_at']

    def __str__(self):
        return f"{self.user.username} - {self.stock.symbol}"


class StockData(models.Model):
    ticker = models.CharField(max_length=10)
    date = models.DateField()
    open_price = models.FloatField()
    close_price = models.FloatField()
    high = models.FloatField()
    low = models.FloatField()
    volume = models.BigIntegerField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('ticker', 'date')
        ordering = ['-date']

    def __str__(self):
        return f"{self.ticker} {self.date}"


class Portfolio(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='portfolios_v2')
    ticker = models.CharField(max_length=10)
    quantity = models.IntegerField()
    purchase_price = models.FloatField()
    purchase_date = models.DateField()

    def __str__(self):
        return f"{self.user.username} - {self.ticker} - {self.quantity} shares"


class PriceAlert(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='price_alerts')
    ticker = models.CharField(max_length=10)
    threshold = models.FloatField()

    def __str__(self):
        return f"{self.user.username} - {self.ticker} - {self.threshold}"
