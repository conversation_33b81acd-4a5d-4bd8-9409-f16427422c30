import yfinance as yf
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
from sklearn.model_selection import train_test_split
import joblib
import os
# import tensorflow as tf
# from tensorflow.keras.models import Sequential
# from tensorflow.keras.layers import LSTM, Dense
# from tensorflow.keras.callbacks import EarlyStopping

MODELS_DIR = os.path.join(os.path.dirname(__file__), 'models')
os.makedirs(MODELS_DIR, exist_ok=True)

def fetch_data(ticker, start_date='2020-01-01', end_date='2025-01-01'):
    df = yf.download(ticker, start=start_date, end=end_date)
    if df.empty:
        raise ValueError(f"No data found for ticker {ticker}")
    return df

def preprocess_data(df):
    df = df[['Open', 'High', 'Low', 'Close', 'Volume']].copy()
    df['Target'] = df['Close'].shift(-1)
    df = df.dropna()
    X = df[['Open', 'High', 'Low', 'Volume']].values
    y = df['Target'].values
    return X, y

def train_linear_regression(X, y, ticker=None):
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)
    model = LinearRegression()
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    print(f"Linear Regression MSE: {mse:.4f}")
    if ticker:
        model_path = os.path.join(MODELS_DIR, f'lr_{ticker.upper()}.joblib')
        joblib.dump(model, model_path)
        print(f"Model saved to {model_path}")
    return model, mse

def load_linear_regression_model(ticker):
    model_path = os.path.join(MODELS_DIR, f'lr_{ticker.upper()}.joblib')
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model for {ticker} not found. Train it first.")
    return joblib.load(model_path)

# def create_lstm_dataset(X, y, time_steps=10):
#     Xs, ys = [], []
#     for i in range(len(X) - time_steps):
#         Xs.append(X[i:i+time_steps])
#         ys.append(y[i+time_steps])
#     return np.array(Xs), np.array(ys)

# def train_lstm(X, y, time_steps=10):
#     X_lstm, y_lstm = create_lstm_dataset(X, y, time_steps)
#     X_train, X_test, y_train, y_test = train_test_split(X_lstm, y_lstm, test_size=0.2, shuffle=False)
#     model = Sequential([
#         LSTM(50, input_shape=(time_steps, X.shape[1]), return_sequences=False),
#         Dense(1)
#     ])
#     model.compile(optimizer='adam', loss='mse')
#     es = EarlyStopping(monitor='val_loss', patience=5, restore_best_weights=True)
#     model.fit(X_train, y_train, epochs=50, batch_size=16, validation_split=0.2, callbacks=[es], verbose=1)
#     y_pred = model.predict(X_test)
#     mse = mean_squared_error(y_test, y_pred)
#     print(f"LSTM MSE: {mse:.4f}")
#     return model, mse

def main(ticker):
    print(f"Fetching data for {ticker}...")
    df = fetch_data(ticker)
    X, y = preprocess_data(df)
    print("Training Linear Regression model...")
    lr_model, lr_mse = train_linear_regression(X, y, ticker)
    # print("Training LSTM model...")
    # lstm_model, lstm_mse = train_lstm(X, y)
    print("Done.")
    return lr_model

if __name__ == "__main__":
    import sys
    ticker = sys.argv[1] if len(sys.argv) > 1 else 'AAPL'
    main(ticker) 