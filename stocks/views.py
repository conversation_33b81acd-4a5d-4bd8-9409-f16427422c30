from rest_framework import viewsets, status, permissions, generics
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.db.models import Q
from .models import Stock, StockPrice, StockPrediction, UserPortfolio, Watchlist, StockData, PriceAlert
from .serializers import (
    UserSerializer, StockSerializer, StockPriceSerializer, StockPredictionSerializer,
    UserPortfolioSerializer, WatchlistSerializer, StockDetailSerializer,
    RegisterSerializer, CustomTokenObtainPairSerializer, CustomTokenRefreshSerializer,
    StockDataSerializer, PriceAlertSerializer
)
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework.views import APIView
import yfinance as yf
from django.utils.dateparse import parse_date
from .ml import load_linear_regression_model, fetch_data, preprocess_data
from .models import Portfolio
from .serializers import PortfolioSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework import viewsets
from django.db.models import F
from django.db.models import Prefetch


class RegisterView(generics.CreateAPIView):
    serializer_class = RegisterSerializer
    permission_classes = [permissions.AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'status': 'success',
                'message': 'Registration successful.',
                'user': UserSerializer(user).data
            }, status=status.HTTP_201_CREATED)
        return Response({
            'status': 'error',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

class CustomLoginView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            return Response({
                'status': 'error',
                'errors': serializer.errors or {'detail': str(e)}
            }, status=status.HTTP_400_BAD_REQUEST)
        return Response({
            'status': 'success',
            'message': 'Login successful.',
            'tokens': serializer.validated_data,
        }, status=status.HTTP_200_OK)

class CustomTokenRefreshView(TokenRefreshView):
    serializer_class = CustomTokenRefreshSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except Exception as e:
            return Response({
                'status': 'error',
                'errors': serializer.errors or {'detail': str(e)}
            }, status=status.HTTP_400_BAD_REQUEST)
        return Response({
            'status': 'success',
            'message': 'Token refreshed.',
            'tokens': serializer.validated_data,
        }, status=status.HTTP_200_OK)


class UserViewSet(viewsets.ModelViewSet):
    """ViewSet for User model"""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current user information"""
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)


class StockViewSet(viewsets.ModelViewSet):
    """ViewSet for Stock model"""
    queryset = Stock.objects.all()
    serializer_class = StockSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return StockDetailSerializer
        return StockSerializer

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Search stocks by symbol or name"""
        query = request.query_params.get('q', '')
        if query:
            stocks = self.queryset.filter(
                Q(symbol__icontains=query) | Q(name__icontains=query)
            )
        else:
            stocks = self.queryset.all()
        
        serializer = self.get_serializer(stocks, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def prices(self, request, pk=None):
        """Get price history for a stock"""
        stock = self.get_object()
        prices = stock.prices.all()
        serializer = StockPriceSerializer(prices, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def predictions(self, request, pk=None):
        """Get predictions for a stock"""
        stock = self.get_object()
        predictions = stock.predictions.filter(is_active=True)
        serializer = StockPredictionSerializer(predictions, many=True)
        return Response(serializer.data)


class StockPriceViewSet(viewsets.ModelViewSet):
    """ViewSet for StockPrice model"""
    queryset = StockPrice.objects.all()
    serializer_class = StockPriceSerializer
    permission_classes = [permissions.IsAuthenticated]


class StockPredictionViewSet(viewsets.ModelViewSet):
    """ViewSet for StockPrediction model"""
    queryset = StockPrediction.objects.filter(is_active=True)
    serializer_class = StockPredictionSerializer
    permission_classes = [permissions.IsAuthenticated]


class UserPortfolioViewSet(viewsets.ModelViewSet):
    """ViewSet for UserPortfolio model"""
    serializer_class = UserPortfolioSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return UserPortfolio.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get portfolio summary"""
        portfolio = self.get_queryset()
        total_value = sum(item.total_value for item in portfolio)
        total_profit_loss = sum(item.profit_loss for item in portfolio)
        
        return Response({
            'total_value': total_value,
            'total_profit_loss': total_profit_loss,
            'total_items': portfolio.count()
        })


class WatchlistViewSet(viewsets.ModelViewSet):
    """ViewSet for Watchlist model"""
    serializer_class = WatchlistSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Watchlist.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['post'])
    def add_stock(self, request):
        """Add a stock to watchlist"""
        stock_id = request.data.get('stock_id')
        notes = request.data.get('notes', '')
        
        try:
            stock = Stock.objects.get(id=stock_id)
            watchlist_item, created = Watchlist.objects.get_or_create(
                user=request.user,
                stock=stock,
                defaults={'notes': notes}
            )
            
            if not created:
                watchlist_item.notes = notes
                watchlist_item.save()
            
            serializer = self.get_serializer(watchlist_item)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        except Stock.DoesNotExist:
            return Response(
                {'error': 'Stock not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['delete'])
    def remove_stock(self, request):
        """Remove a stock from watchlist"""
        stock_id = request.data.get('stock_id')
        
        try:
            watchlist_item = Watchlist.objects.get(
                user=request.user,
                stock_id=stock_id
            )
            watchlist_item.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        
        except Watchlist.DoesNotExist:
            return Response(
                {'error': 'Stock not in watchlist'}, 
                status=status.HTTP_404_NOT_FOUND
            )

class StockDataFetchView(APIView):
    def get(self, request, ticker):
        ticker = ticker.upper()
        try:
            # Try to get recent data from DB first
            recent_data = StockData.objects.filter(ticker=ticker).order_by('-date')[:30]
            if recent_data.exists():
                serializer = StockDataSerializer(recent_data, many=True)
                return Response(serializer.data)
            # Fetch from yfinance if not in DB
            stock = yf.Ticker(ticker)
            hist = stock.history(period="1mo")
            if hist.empty:
                return Response({"error": "Invalid ticker symbol or no data available."}, status=400)
            data_objs = []
            for date, row in hist.iterrows():
                obj, _ = StockData.objects.get_or_create(
                    ticker=ticker,
                    date=date.date(),
                    defaults={
                        'open_price': float(row['Open']),
                        'close_price': float(row['Close']),
                        'high': float(row['High']),
                        'low': float(row['Low']),
                        'volume': int(row['Volume']),
                    }
                )
                data_objs.append(obj)
            serializer = StockDataSerializer(data_objs, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response({"error": "Invalid ticker symbol or data fetch error."}, status=400)

class StockPredictView(APIView):
    def get(self, request, ticker):
        ticker = ticker.upper()
        try:
            model = load_linear_regression_model(ticker)
        except FileNotFoundError:
            return Response({"error": f"Model for {ticker} not found. Train it first."}, status=404)
        try:
            df = fetch_data(ticker)
            X, y = preprocess_data(df)
            latest_features = X[-1].reshape(1, -1)
            pred = model.predict(latest_features)[0]
            return Response({
                "ticker": ticker,
                "predicted_next_close": float(pred)
            })
        except Exception as e:
            return Response({"error": f"Prediction failed: {str(e)}"}, status=400)

class PortfolioViewSet(viewsets.ModelViewSet):
    serializer_class = PortfolioSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Portfolio.objects.filter(user=self.request.user).select_related('user')

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    # Add current price and profit/loss to the response
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset().prefetch_related(
            Prefetch('user'),
        )
        data = []
        for item in queryset:
            # Get latest close price from StockData
            stockdata = StockData.objects.filter(ticker=item.ticker).order_by('-date').first()
            current_price = stockdata.close_price if stockdata else None
            profit_loss = None
            if current_price is not None:
                profit_loss = (current_price - item.purchase_price) * item.quantity
            row = PortfolioSerializer(item).data
            row['current_price'] = current_price
            row['profit_loss'] = profit_loss
            data.append(row)
        return Response(data)

class PriceAlertViewSet(viewsets.ModelViewSet):
    serializer_class = PriceAlertSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return PriceAlert.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
