from celery import shared_task
from .models import StockData
import yfinance as yf
from datetime import datetime
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

TICKERS = ['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'AMZN']

@shared_task(bind=True, max_retries=3, default_retry_delay=60*5)
def update_stock_data(self):
    logger.info('Starting stock data update task...')
    for ticker in TICKERS:
        try:
            stock = yf.Ticker(ticker)
            hist = stock.history(period="5d")
            if hist.empty:
                logger.warning(f"No data for {ticker}")
                continue
            for date, row in hist.iterrows():
                obj, created = StockData.objects.update_or_create(
                    ticker=ticker,
                    date=date.date(),
                    defaults={
                        'open_price': float(row['Open']),
                        'close_price': float(row['Close']),
                        'high': float(row['High']),
                        'low': float(row['Low']),
                        'volume': int(row['Volume']),
                        'created_at': timezone.now(),
                    }
                )
                logger.info(f"{'Created' if created else 'Updated'} {ticker} {date.date()}")
        except Exception as exc:
            logger.error(f"Error updating {ticker}: {exc}")
            self.retry(exc=exc)
    logger.info('Stock data update task completed.') 