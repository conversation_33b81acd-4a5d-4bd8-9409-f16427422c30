from django.contrib import admin
from .models import Stock, StockPrice, StockPrediction, UserPortfolio, Watchlist, Profile


@admin.register(Stock)
class StockAdmin(admin.ModelAdmin):
    list_display = ['symbol', 'name', 'sector', 'industry', 'created_at']
    list_filter = ['sector', 'industry', 'created_at']
    search_fields = ['symbol', 'name']
    ordering = ['symbol']


@admin.register(StockPrice)
class StockPriceAdmin(admin.ModelAdmin):
    list_display = ['stock', 'date', 'close_price', 'volume', 'created_at']
    list_filter = ['stock', 'date', 'created_at']
    search_fields = ['stock__symbol', 'stock__name']
    ordering = ['-date']


@admin.register(StockPrediction)
class StockPredictionAdmin(admin.ModelAdmin):
    list_display = ['stock', 'prediction_date', 'predicted_price', 'confidence_score', 'model_used', 'is_active']
    list_filter = ['stock', 'model_used', 'is_active', 'prediction_date']
    search_fields = ['stock__symbol', 'stock__name']
    ordering = ['-prediction_date']


@admin.register(UserPortfolio)
class UserPortfolioAdmin(admin.ModelAdmin):
    list_display = ['user', 'stock', 'shares', 'average_price', 'total_value', 'profit_loss']
    list_filter = ['user', 'stock', 'created_at']
    search_fields = ['user__username', 'stock__symbol']
    ordering = ['-updated_at']


@admin.register(Watchlist)
class WatchlistAdmin(admin.ModelAdmin):
    list_display = ['user', 'stock', 'added_at', 'notes']
    list_filter = ['user', 'stock', 'added_at']
    search_fields = ['user__username', 'stock__symbol', 'notes']
    ordering = ['-added_at']


@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'email_notifications']
    search_fields = ['user__username', 'user__email']
