from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    UserViewSet, StockViewSet, StockPriceViewSet, StockPredictionViewSet,
    UserPortfolioViewSet, WatchlistViewSet,
    RegisterView, CustomLoginView, CustomTokenRefreshView,
    StockDataFetchView, StockPredictView, PortfolioViewSet, PriceAlertViewSet
)

router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'stocks', StockViewSet)
router.register(r'stock-prices', StockPriceViewSet)
router.register(r'predictions', StockPredictionViewSet)
router.register(r'portfolio', UserPortfolioViewSet, basename='portfolio')
router.register(r'watchlist', WatchlistViewSet, basename='watchlist')
router.register(r'portfolios', PortfolioViewSet, basename='portfolio-v2')
router.register(r'alerts', PriceAlertViewSet, basename='pricealert')

urlpatterns = [
    path('api/', include(router.urls)),
    path('api/auth/register/', RegisterView.as_view(), name='register'),
    path('api/auth/login/', CustomLoginView.as_view(), name='login'),
    path('api/auth/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),
    path('api/stocks/<str:ticker>/', StockDataFetchView.as_view(), name='stockdata-fetch'),
    path('api/predict/<str:ticker>/', StockPredictView.as_view(), name='stock-predict'),
] 