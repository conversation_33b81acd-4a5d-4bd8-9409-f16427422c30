from django.core.management.base import BaseCommand
from stocks.tasks import update_stock_data

class Command(BaseCommand):
    help = 'Trigger the Celery task to update stock data for all tickers.'

    def handle(self, *args, **options):
        self.stdout.write(self.style.NOTICE('Triggering stock data update task...'))
        result = update_stock_data.delay()
        self.stdout.write(self.style.SUCCESS(f'Task triggered. Task ID: {result.id}'))
        self.stdout.write('Check your Celery worker logs for progress and results.') 