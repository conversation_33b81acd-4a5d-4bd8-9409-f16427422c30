#!/usr/bin/env python3
"""
<PERSON>ript to fix common issues in the Stock Prediction App
Run this after setting up the virtual environment and installing dependencies
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock_predictor.settings')
    django.setup()

def create_missing_directories():
    """Create missing directories"""
    directories = [
        'stocks/models',
        'static',
        'media',
        'frontend/dist',
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created directory: {directory}")

def fix_database_issues():
    """Fix database and migration issues"""
    print("🔧 Fixing database issues...")
    
    try:
        # Make migrations
        execute_from_command_line(['manage.py', 'makemigrations'])
        print("✅ Created migrations")
        
        # Apply migrations
        execute_from_command_line(['manage.py', 'migrate'])
        print("✅ Applied migrations")
        
    except Exception as e:
        print(f"❌ Database fix failed: {e}")

def create_superuser():
    """Create default superuser if it doesn't exist"""
    try:
        from django.contrib.auth.models import User
        
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin')
            print("✅ Created default superuser (admin/admin)")
        else:
            print("ℹ️  Superuser already exists")
            
    except Exception as e:
        print(f"❌ Superuser creation failed: {e}")

def create_sample_data():
    """Create sample stock data for testing"""
    try:
        from stocks.models import Stock, StockPrice
        from datetime import date, timedelta
        from decimal import Decimal
        
        # Create sample stocks
        sample_stocks = [
            {'symbol': 'AAPL', 'name': 'Apple Inc.', 'sector': 'Technology', 'industry': 'Consumer Electronics'},
            {'symbol': 'GOOGL', 'name': 'Alphabet Inc.', 'sector': 'Technology', 'industry': 'Internet Services'},
            {'symbol': 'MSFT', 'name': 'Microsoft Corporation', 'sector': 'Technology', 'industry': 'Software'},
            {'symbol': 'TSLA', 'name': 'Tesla Inc.', 'sector': 'Consumer Cyclical', 'industry': 'Auto Manufacturers'},
        ]
        
        for stock_data in sample_stocks:
            stock, created = Stock.objects.get_or_create(
                symbol=stock_data['symbol'],
                defaults=stock_data
            )
            if created:
                print(f"✅ Created stock: {stock.symbol}")
                
                # Create sample price data
                base_price = Decimal('150.00')
                for i in range(30):  # Last 30 days
                    price_date = date.today() - timedelta(days=i)
                    price_variation = Decimal(str(i * 0.5))  # Simple price variation
                    
                    StockPrice.objects.get_or_create(
                        stock=stock,
                        date=price_date,
                        defaults={
                            'open_price': base_price + price_variation,
                            'high_price': base_price + price_variation + Decimal('5.00'),
                            'low_price': base_price + price_variation - Decimal('3.00'),
                            'close_price': base_price + price_variation + Decimal('1.00'),
                            'volume': 1000000 + (i * 10000)
                        }
                    )
            
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")

def check_frontend_build():
    """Check if frontend is built"""
    if not os.path.exists('frontend/dist/index.html'):
        print("⚠️  Frontend not built. Run 'cd frontend && npm run build' to build the frontend")
    else:
        print("✅ Frontend build exists")

def main():
    """Main function to run all fixes"""
    print("🚀 Starting Stock Predictor App fixes...")
    
    # Check if we're in the right directory
    if not os.path.exists('manage.py'):
        print("❌ Error: manage.py not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Setup Django
    setup_django()
    
    # Run fixes
    create_missing_directories()
    fix_database_issues()
    create_superuser()
    create_sample_data()
    check_frontend_build()
    
    print("\n🎉 Fixes completed!")
    print("\nNext steps:")
    print("1. Install frontend dependencies: cd frontend && npm install")
    print("2. Build frontend: npm run build")
    print("3. Start Django server: python manage.py runserver")
    print("4. Start frontend dev server: cd frontend && npm run dev")
    print("\nAccess points:")
    print("- Frontend: http://localhost:3000")
    print("- Backend API: http://localhost:8000")
    print("- Admin Panel: http://localhost:8000/admin (admin/admin)")

if __name__ == '__main__':
    main()
