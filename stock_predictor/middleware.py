from django.http import JsonResponse
from django.urls import resolve
from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON><PERSON>ication
from rest_framework.exceptions import AuthenticationFailed

class EnforceJWTOnAPI:
    def __init__(self, get_response):
        self.get_response = get_response
        self.jwt_auth = JWTAuthentication()

    def __call__(self, request):
        path = request.path
        if path.startswith('/api/') and not path.startswith('/api/auth/'):
            try:
                user_auth_tuple = self.jwt_auth.authenticate(request)
                if user_auth_tuple is not None:
                    request.user, request.auth = user_auth_tuple
                else:
                    return JsonResponse({'detail': 'Authentication credentials were not provided.'}, status=401)
            except AuthenticationFailed as e:
                return JsonResponse({'detail': str(e)}, status=401)
        return self.get_response(request) 