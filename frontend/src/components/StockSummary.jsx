import React, { useEffect, useState } from 'react';
import axios from 'axios';

const StockSummary = ({ ticker }) => {
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [lastUpdated, setLastUpdated] = useState(null);

  const fetchSummary = async () => {
    setLoading(true);
    setError('');
    try {
      const token = localStorage.getItem('access_token');
      const res = await axios.get(`/api/stocks/${ticker}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setSummary(res.data);
      setLastUpdated(new Date().toLocaleString());
    } catch (err) {
      setError('Failed to fetch stock summary.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSummary();
    // eslint-disable-next-line
  }, [ticker]);

  return (
    <div className="bg-white rounded-lg shadow p-4 max-w-md mx-auto mt-6">
      <div className="flex items-center justify-between mb-2">
        <h2 className="text-lg font-semibold">{ticker} Stock Summary</h2>
        <button
          onClick={fetchSummary}
          className="text-xs px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Refresh
        </button>
      </div>
      <div className="text-xs text-gray-500 mb-2">
        Data cached, refresh for latest. Last updated: {lastUpdated || '...'}
      </div>
      {loading ? (
        <div className="flex justify-center items-center h-20">
          <svg className="animate-spin h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
          </svg>
        </div>
      ) : error ? (
        <div className="text-red-500 text-sm">{error}</div>
      ) : summary ? (
        <div>
          <div className="flex justify-between py-1">
            <span className="font-medium">Latest Close:</span>
            <span>${summary[summary.length-1]?.close_price?.toFixed(2)}</span>
          </div>
          <div className="flex justify-between py-1">
            <span className="font-medium">Volume:</span>
            <span>{summary[summary.length-1]?.volume?.toLocaleString()}</span>
          </div>
        </div>
      ) : null}
      <div className="text-xs text-gray-400 mt-2">This summary is cached for performance. Click refresh for the latest data.</div>
    </div>
  );
};

export default StockSummary; 