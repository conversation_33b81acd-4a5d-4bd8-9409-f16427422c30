import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import Login from './Login';
import Register from './Register';

const AuthPage = () => {
  const location = useLocation();
  const [showLogin, setShowLogin] = useState(location.pathname === '/login');

  return showLogin ? (
    <Login onToggle={() => setShowLogin(false)} />
  ) : (
    <Register onToggle={() => setShowLogin(true)} />
  );
};

export default AuthPage;