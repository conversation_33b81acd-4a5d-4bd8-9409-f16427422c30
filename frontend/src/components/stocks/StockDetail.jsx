import React from 'react';
import { useParams } from 'react-router-dom';

const StockDetail = () => {
  const { id } = useParams();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Stock Details</h1>
        <p className="text-gray-600">Stock ID: {id}</p>
      </div>
      
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Stock Information</h2>
        <p className="text-gray-600">Stock detail component coming soon...</p>
      </div>
    </div>
  );
};

export default StockDetail; 