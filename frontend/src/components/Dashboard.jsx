import React, { useEffect, useState } from 'react';
import axios from 'axios';
import {
  LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Legend, Dot
} from 'recharts';
import { ArrowPathIcon } from '@heroicons/react/24/outline';

const TICKERS = ['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'AMZN'];

const Dashboard = () => {
  const [ticker, setTicker] = useState('AAPL');
  const [data, setData] = useState([]);
  const [prediction, setPrediction] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const fetchData = async (selectedTicker = ticker) => {
    setLoading(true);
    setError('');
    try {
      const token = localStorage.getItem('access_token');
      const [pricesRes, predRes] = await Promise.all([
        axios.get(`/api/stocks/${selectedTicker}/`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
        axios.get(`/api/predict/${selectedTicker}/`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
      ]);
      setData(pricesRes.data.reverse()); // oldest to newest
      setPrediction(predRes.data.predicted_next_close);
    } catch (err) {
      setError(
        err.response?.data?.error || 'Failed to fetch stock data or prediction.'
      );
      setData([]);
      setPrediction(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(ticker);
    // eslint-disable-next-line
  }, [ticker]);

  const handleRefresh = () => fetchData(ticker);

  // Prepare chart data with prediction
  let chartData = data.map(d => ({ ...d, type: 'actual' }));
  if (prediction && data.length > 0) {
    const lastDate = data[data.length - 1].date;
    // Add prediction as the next day (fake date for chart continuity)
    const nextDate = new Date(lastDate);
    nextDate.setDate(nextDate.getDate() + 1);
    chartData = [
      ...chartData,
      {
        date: nextDate.toISOString().slice(0, 10),
        close_price: prediction,
        type: 'predicted',
      },
    ];
  }

  return (
    <div className="max-w-2xl mx-auto p-4">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
        <div className="flex items-center gap-2">
          <label htmlFor="ticker" className="text-sm font-medium text-gray-700">Select Ticker:</label>
          <select
            id="ticker"
            value={ticker}
            onChange={e => setTicker(e.target.value)}
            className="rounded border-gray-300 focus:ring-green-500 focus:border-green-500 text-sm"
          >
            {TICKERS.map(t => (
              <option key={t} value={t}>{t}</option>
            ))}
          </select>
        </div>
        <button
          onClick={handleRefresh}
          className="flex items-center gap-1 px-3 py-1.5 bg-green-500 text-white rounded hover:bg-green-600 transition"
        >
          <ArrowPathIcon className="h-4 w-4" /> Refresh
        </button>
      </div>
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded mb-4 text-sm">{error}</div>
      )}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <svg className="animate-spin h-10 w-10 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
          </svg>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-2 text-center">{ticker} Price Chart</h2>
          <ResponsiveContainer width="100%" height={320}>
            <LineChart data={chartData} margin={{ top: 20, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" minTickGap={30} tick={{ fontSize: 12 }} />
              <YAxis domain={['auto', 'auto']} tick={{ fontSize: 12 }} />
              <Tooltip
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length) {
                    const d = payload[0].payload;
                    if (d.type === 'predicted') {
                      return (
                        <div className="bg-white border border-red-400 text-red-700 px-3 py-2 rounded shadow text-xs">
                          <div><b>{label}</b></div>
                          <div>Predicted Close: <b>{d.close_price.toFixed(2)}</b></div>
                          <div className="mt-1 text-red-500">This is the ML-predicted price for the next trading day.</div>
                        </div>
                      );
                    }
                    return (
                      <div className="bg-white border border-gray-300 px-3 py-2 rounded shadow text-xs">
                        <div><b>{label}</b></div>
                        <div>Close: <b>{d.close_price.toFixed(2)}</b></div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Legend verticalAlign="top" height={36} />
              <Line
                type="monotone"
                dataKey="close_price"
                stroke="#4CAF50"
                strokeWidth={2}
                dot={false}
                name="Close Price"
                isAnimationActive={false}
              />
              {prediction && (
                <Line
                  type="monotone"
                  dataKey="close_price"
                  data={chartData.filter(d => d.type === 'predicted')}
                  stroke="red"
                  strokeWidth={0}
                  dot={{ stroke: 'red', strokeWidth: 2, fill: 'red', r: 6 }}
                  name="Predicted Price"
                  legendType="circle"
                  isAnimationActive={false}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  );
};

export default Dashboard; 