import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import Dashboard from '../Dashboard';
import axios from 'axios';

jest.mock('axios');

describe('Dashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders chart and refresh button', async () => {
    axios.get.mockImplementation((url) => {
      if (url.includes('/api/stocks/')) {
        return Promise.resolve({ data: [
          { date: '2025-01-01', close_price: 100, volume: 1000 },
          { date: '2025-01-02', close_price: 110, volume: 1200 },
        ] });
      }
      if (url.includes('/api/predict/')) {
        return Promise.resolve({ data: { predicted_next_close: 120 } });
      }
      return Promise.reject();
    });
    render(<Dashboard />);
    expect(screen.getByText(/Select Ticker/i)).toBeInTheDocument();
    await waitFor(() => expect(screen.getByText(/Price Chart/i)).toBeInTheDocument());
    expect(screen.getByRole('button', { name: /Refresh/i })).toBeInTheDocument();
  });

  it('shows error message on API error', async () => {
    axios.get.mockRejectedValue(new Error('API error'));
    render(<Dashboard />);
    await waitFor(() => expect(screen.getByText(/Failed to fetch/i)).toBeInTheDocument());
  });
}); 