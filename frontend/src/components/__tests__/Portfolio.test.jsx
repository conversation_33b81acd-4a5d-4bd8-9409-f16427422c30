import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import Portfolio from '../Portfolio';
import axios from 'axios';

jest.mock('axios');

describe('Portfolio', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders portfolio table and profit/loss', async () => {
    axios.get.mockResolvedValue({ data: [
      { id: 1, ticker: 'AAPL', quantity: 10, purchase_price: 100, purchase_date: '2023-01-01', current_price: 120, profit_loss: 200 },
      { id: 2, ticker: 'TSLA', quantity: 5, purchase_price: 200, purchase_date: '2023-01-01', current_price: 180, profit_loss: -100 },
    ] });
    render(<Portfolio />);
    await waitFor(() => expect(screen.getByText('AAPL')).toBeInTheDocument());
    expect(screen.getByText('TSLA')).toBeInTheDocument();
    expect(screen.getByText('200.00')).toBeInTheDocument(); // profit
    expect(screen.getByText('-100.00')).toBeInTheDocument(); // loss
  });

  it('shows validation error for negative quantity', async () => {
    axios.get.mockResolvedValue({ data: [] });
    axios.post.mockRejectedValue({ response: { data: { quantity: ['Quantity must be positive.'] } } });
    render(<Portfolio />);
    fireEvent.change(screen.getByLabelText(/Ticker/i), { target: { value: 'AAPL' } });
    fireEvent.change(screen.getByLabelText(/Quantity/i), { target: { value: '-5' } });
    fireEvent.change(screen.getByLabelText(/Purchase Price/i), { target: { value: '100' } });
    fireEvent.change(screen.getByLabelText(/Purchase Date/i), { target: { value: '2023-01-01' } });
    fireEvent.click(screen.getByRole('button', { name: /Add/i }));
    await waitFor(() => expect(screen.getByText(/Quantity must be positive/i)).toBeInTheDocument());
  });
}); 