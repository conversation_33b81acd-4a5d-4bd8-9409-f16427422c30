import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        const response = await axios.post(`${API_BASE_URL}/token/refresh/`, {
          refresh: refreshToken,
        });

        const { access } = response.data;
        localStorage.setItem('access_token', access);

        originalRequest.headers.Authorization = `Bearer ${access}`;
        return api(originalRequest);
      } catch (refreshError) {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/token/', credentials),
  refresh: (refreshToken) => api.post('/token/refresh/', { refresh: refreshToken }),
  me: () => api.get('/users/me/'),
};

// Stocks API
export const stocksAPI = {
  getAll: () => api.get('/stocks/'),
  getById: (id) => api.get(`/stocks/${id}/`),
  search: (query) => api.get(`/stocks/search/?q=${query}`),
  getPrices: (id) => api.get(`/stocks/${id}/prices/`),
  getPredictions: (id) => api.get(`/stocks/${id}/predictions/`),
};

// Portfolio API
export const portfolioAPI = {
  getAll: () => api.get('/portfolio/'),
  getSummary: () => api.get('/portfolio/summary/'),
  create: (data) => api.post('/portfolio/', data),
  update: (id, data) => api.put(`/portfolio/${id}/`, data),
  delete: (id) => api.delete(`/portfolio/${id}/`),
};

// Watchlist API
export const watchlistAPI = {
  getAll: () => api.get('/watchlist/'),
  addStock: (stockId, notes = '') => api.post('/watchlist/add_stock/', { stock_id: stockId, notes }),
  removeStock: (stockId) => api.delete('/watchlist/remove_stock/', { data: { stock_id: stockId } }),
  create: (data) => api.post('/watchlist/', data),
  update: (id, data) => api.put(`/watchlist/${id}/`, data),
  delete: (id) => api.delete(`/watchlist/${id}/`),
};

// Predictions API
export const predictionsAPI = {
  getAll: () => api.get('/predictions/'),
  getById: (id) => api.get(`/predictions/${id}/`),
};

export default api; 