#!/bin/bash

echo "🚀 Setting up Stock Predictor Project..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Backend Setup
echo "📦 Setting up Django backend..."

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install Python dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Run migrations
echo "Running database migrations..."
python manage.py makemigrations
python manage.py migrate

# Create superuser if it doesn't exist
echo "Creating superuser (admin/admin)..."
echo "from django.contrib.auth.models import User; User.objects.create_superuser('admin', '<EMAIL>', 'admin') if not User.objects.filter(username='admin').exists() else None" | python manage.py shell

# Run fix script to address any remaining issues
echo "Running fix script..."
python fix_issues.py

echo "✅ Backend setup complete!"

# Frontend Setup
echo "📦 Setting up React frontend..."

cd frontend

# Install Node.js dependencies
echo "Installing Node.js dependencies..."
npm install

# Build the frontend
echo "Building frontend..."
npm run build

cd ..

echo "✅ Frontend setup complete!"

echo ""
echo "🎉 Setup complete!"
echo ""
echo "To start the development servers:"
echo ""
echo "Backend (Django):"
echo "  source venv/bin/activate"
echo "  python manage.py runserver"
echo ""
echo "Frontend (React):"
echo "  cd frontend"
echo "  npm run dev"
echo ""
echo "Access the application:"
echo "  Frontend: http://localhost:3000"
echo "  Backend API: http://localhost:8000"
echo "  Admin: http://localhost:8000/admin (admin/admin)"
echo ""
echo "Happy coding! 🚀" 