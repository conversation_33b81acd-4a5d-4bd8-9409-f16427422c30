# syntax=docker/dockerfile:1
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y build-essential libpq-dev curl && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt ./
RUN pip install --upgrade pip && pip install -r requirements.txt

# Copy Django app
COPY . .

# Copy built frontend (assume built before docker build)
RUN mkdir -p /app/staticfiles && \
    if [ -d "frontend/dist" ]; then cp -r frontend/dist/* /app/staticfiles/; fi

# Collect static files
RUN python manage.py collectstatic --noinput

# Expose port
EXPOSE 8000

# Use environment variables for sensitive data
ENV DJANGO_SECRET_KEY=changeme
ENV EMAIL_HOST_PASSWORD=changeme

# Start Gunicorn
CMD ["gunicorn", "stock_predictor.wsgi:application", "--bind", "0.0.0.0:8000"] 