# Django Settings
SECRET_KEY=django-insecure-your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
# Use 'sqlite' for development, 'postgres' for production
DB_BACKEND=sqlite

# PostgreSQL settings (only needed if DB_BACKEND=postgres)
DB_NAME=stock_predictor
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# Security Settings (only for production)
SECURE_SSL_REDIRECT=False

# Cache Settings
REDIS_URL=redis://localhost:6379/1

# API Keys (add your API keys here)
# ALPHA_VANTAGE_API_KEY=your_api_key_here
# FINNHUB_API_KEY=your_api_key_here
